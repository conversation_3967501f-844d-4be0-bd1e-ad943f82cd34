import * as yup from 'yup';

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const resaleCertSchema = yup.object().shape({
    resaleCertFile: yup.string().required('Resale certificate file is required'),
    resaleCertFileUrl: yup.string().required('Resale certificate file is required'),
    state_id: yup.string().required('State is required'),
    expiration_date: yup.string().required('Expiration date is required'),
    state_list: yup.array().of(yup.string()).default([])
});

export type resaleFormData = yup.InferType<typeof resaleCertSchema>;
