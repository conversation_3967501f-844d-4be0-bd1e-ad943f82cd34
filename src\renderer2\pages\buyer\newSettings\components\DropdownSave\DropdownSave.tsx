import React, { useState } from 'react';
import { Select, MenuItem, FormControl } from '@mui/material';
import styles from './Dropdown.module.scss';

interface DropdownSaveButtonProps {
    onSave: () => void;
    onSaveAndNext?: () => void;
    onSaveAndExit?: () => void;
    isDisabled?: boolean;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    className?: string;
    buttonText?: string;
    dropdownOptions?: {
        save?: string;
        saveAndNext?: string;
        saveAndExit?: string;
    };
}

const DropdownSave: React.FC<DropdownSaveButtonProps> = ({
    onSave,
    onSaveAndNext,
    onSaveAndExit,
    isDisabled = false,
    position = 'top-right',
    className = '',
    buttonText = 'Save',
    dropdownOptions = {
        save: 'Save',
        saveAndNext: 'Save & Next',
        saveAndExit: 'Save & Exit'
    }
}) => {
    const [selectedValue, setSelectedValue] = useState('save');

    // Create menu items based on available actions
    const menuItems = [
        { value: 'save', label: dropdownOptions.save, action: onSave },
        ...(onSaveAndNext ? [{ value: 'saveAndNext', label: dropdownOptions.saveAndNext, action: onSaveAndNext }] : []),
        ...(onSaveAndExit ? [{ value: 'saveAndExit', label: dropdownOptions.saveAndExit, action: onSaveAndExit }] : [])
    ];

    const handleChange = (event: any) => {
        const value = event.target.value;
        setSelectedValue(value);

        // Find and execute the corresponding action
        const selectedItem = menuItems.find(item => item.value === value);
        if (selectedItem) {
            selectedItem.action();
        }
    };

    const CustomArrowIcon = () => (
        <svg
            width="12"
            height="12"
            viewBox="0 0 24 24"
            fill="currentColor"
            style={{ color: 'white' }}
        >
            <path d="M7 10l5 5 5-5z"/>
        </svg>
    );

    return (
        <div className={className}>
            <FormControl className={styles.formControl}>
                <Select
                    value={selectedValue}
                    onChange={handleChange}
                    disabled={isDisabled}
                    className={styles.muiSelect}
                    IconComponent={CustomArrowIcon}
                    MenuProps={{
                        classes: {
                            paper: styles.menuPaper,
                            list: styles.menuList
                        },
                        anchorOrigin: {
                            vertical: 'bottom',
                            horizontal: 'right',
                        },
                        transformOrigin: {
                            vertical: 'top',
                            horizontal: 'right',
                        },
                    }}
                    renderValue={(value) => {
                        const item = menuItems.find(item => item.value === value);
                        return item ? item.label : buttonText;
                    }}
                >
                    {menuItems.map((item) => (
                        <MenuItem
                            key={item.value}
                            value={item.value}
                            className={styles.menuItem}
                        >
                            {item.label}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
        </div>
    );
};

export default DropdownSave;
