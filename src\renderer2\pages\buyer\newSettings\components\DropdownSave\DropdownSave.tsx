import React, { useState, useRef } from 'react';
import { ClickAwayListener } from '@mui/material';
import styles from './Dropdown.module.scss';

interface DropdownSaveButtonProps {
    onSave: () => void;
    onSaveAndNext?: () => void;
    onSaveAndExit?: () => void;
    isDisabled?: boolean;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
    className?: string;
    buttonText?: string;
    dropdownOptions?: {
        save?: string;
        saveAndNext?: string;
        saveAndExit?: string;
    };
}

const DropdownSave: React.FC<DropdownSaveButtonProps> = ({
    onSave,
    onSaveAndNext,
    onSaveAndExit,
    isDisabled = false,
    position = 'top-right',
    className = '',
    buttonText = 'Save',
    dropdownOptions = {
        save: 'Save',
        saveAndNext: 'Save & Next',
        saveAndExit: 'Save & Exit'
    }
}) => {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Create menu items based on available actions
    const menuItems = [
        { value: 'save', label: dropdownOptions.save, action: onSave },
        ...(onSaveAndNext ? [{ value: 'saveAndNext', label: dropdownOptions.saveAndNext, action: onSaveAndNext }] : []),
        ...(onSaveAndExit ? [{ value: 'saveAndExit', label: dropdownOptions.saveAndExit, action: onSaveAndExit }] : [])
    ];

    const handleDropdownToggle = () => {
        if (isDisabled) return;
        setIsDropdownOpen(!isDropdownOpen);
    };

    const handleClickAway = () => {
        setIsDropdownOpen(false);
    };

    const handleItemClick = (action: () => void) => {
        action();
        setIsDropdownOpen(false);
    };

    const CustomArrowIcon = () => (
        <svg
            width="12"
            height="12"
            viewBox="0 0 24 24"
            fill="currentColor"
            style={{
                color: 'white',
                transform: isDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.2s ease'
            }}
        >
            <path d="M7 10l5 5 5-5z"/>
        </svg>
    );

    return (
        <ClickAwayListener onClickAway={handleClickAway}>
            <div ref={dropdownRef} className={className}>
                <div className={styles.selectContainer}>
                    <button
                        onClick={handleDropdownToggle}
                        disabled={isDisabled}
                        className={styles.selectButton}
                    >
                        <span className={styles.selectText}>{buttonText}</span>
                        <CustomArrowIcon />
                    </button>

                    {isDropdownOpen && !isDisabled && (
                        <div className={styles.dropdownMenu}>
                            {menuItems.map((item) => (
                                <button
                                    key={item.value}
                                    onClick={() => handleItemClick(item.action)}
                                    className={styles.dropdownItem}
                                >
                                    {item.label}
                                </button>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </ClickAwayListener>
    );
};

export default DropdownSave;
