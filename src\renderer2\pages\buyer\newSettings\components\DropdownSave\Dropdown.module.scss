// Custom Select Container
.selectContainer {
    position: relative;
    width: auto;
    min-width: 120px;
}

// Custom Select Button (looks like MUI Select)
.selectButton {
    background-color: #2c2c2c;
    border: none;
    border-radius: 5px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 32px 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-height: 36px;
    transition: background-color 0.2s ease;

    &:hover:not(:disabled) {
        background-color: #3a3a3a;
    }

    &:focus {
        outline: none;
        background-color: #3a3a3a;
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #1a1a1a;
        color: #666;
    }
}

// Select Text
.selectText {
    flex: 1;
    text-align: left;
    color: white;
}

// Dropdown Menu (positioned absolutely)
.dropdownMenu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #2c2c2c;
    border-radius: 5px;
    margin-top: 2px;
    min-width: 150px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border: 1px solid #404040;
    overflow: hidden;
}

// Dropdown Items (action buttons)
.dropdownItem {
    width: 100%;
    background-color: #2c2c2c;
    color: white;
    border: none;
    padding: 10px 16px;
    font-size: 14px;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #404040;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: #3a3a3a;
    }

    &:focus {
        outline: none;
        background-color: #3a3a3a;
    }

    &:active {
        background-color: #4a4a4a;
    }
}
