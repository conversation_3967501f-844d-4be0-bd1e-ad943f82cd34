// MUI Select Form Control
.formControl {
    width: auto;
    min-width: 120px;
}

// MUI Select Component Styling
.muiSelect {
    background-color: #2c2c2c !important;
    border-radius: 5px !important;
    color: white !important;
    font-size: 14px !important;
    font-weight: 500 !important;

    // Remove default border
    .MuiOutlinedInput-notchedOutline {
        border: none !important;
    }

    // Select field styling
    .MuiSelect-select {
        padding: 8px 32px 8px 12px !important;
        color: white !important;
        background-color: #2c2c2c !important;
        border-radius: 5px !important;
        display: flex !important;
        align-items: center !important;
        min-height: auto !important;
    }

    // Icon styling
    .MuiSelect-icon {
        color: white !important;
        right: 8px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
    }

    // Hover state
    &:hover {
        .MuiSelect-select {
            background-color: #3a3a3a !important;
        }
    }

    // Focus state
    &.Mui-focused {
        .MuiSelect-select {
            background-color: #3a3a3a !important;
        }
        .MuiOutlinedInput-notchedOutline {
            border: none !important;
        }
    }

    // Disabled state
    &.Mui-disabled {
        opacity: 0.5;
        cursor: not-allowed;

        .MuiSelect-select {
            background-color: #1a1a1a !important;
            color: #666 !important;
        }
    }
}

// Menu Paper (dropdown container)
.menuPaper {
    background-color: #2c2c2c !important;
    border-radius: 5px !important;
    margin-top: 2px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
    border: 1px solid #404040 !important;
    min-width: 150px !important;
}

// Menu List
.menuList {
    padding: 0 !important;
}

// Menu Items
.menuItem {
    background-color: #2c2c2c !important;
    color: white !important;
    padding: 10px 16px !important;
    font-size: 14px !important;
    border-bottom: 1px solid #404040 !important;
    transition: background-color 0.2s ease !important;

    &:last-child {
        border-bottom: none !important;
    }

    &:hover {
        background-color: #3a3a3a !important;
    }

    &.Mui-selected {
        background-color: #404040 !important;

        &:hover {
            background-color: #4a4a4a !important;
        }
    }
}
