.buttonContainer {
    display: flex;
    align-items: center;
    background-color: black;
    border-radius: 5px;
    overflow: hidden;
    opacity: 1;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.saveButton {
    color: white;
    background-color: black;
    border: none;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s ease;

    &:disabled {
        cursor: not-allowed;
    }

    &:hover:not(:disabled) {
        background-color: #333;
    }
}

.separator {
    width: 1px;
    height: 20px;
    background-color: #666;
    margin: 0 2px;
}

.dropdownButton {
    color: white;
    background-color: black;
    border: none;
    padding: 5px 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;

    &:disabled {
        cursor: not-allowed;
    }

    &:hover:not(:disabled) {
        background-color: #333;
    }
}

.dropdownIcon {
    transition: transform 0.2s ease;
}

.dropdownMenu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: black;
    border-radius: 5px;
    margin-top: 2px;
    min-width: 150px;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dropdownItem {
    width: 100%;
    padding: 8px 12px;
    background-color: black;
    color: white;
    border: none;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;

    &:not(:last-child) {
        border-bottom: 1px solid #333;
    }

    &:hover {
        background-color: #333;
    }
}
