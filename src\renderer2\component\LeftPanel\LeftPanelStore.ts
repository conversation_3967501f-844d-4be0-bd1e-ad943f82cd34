// @ts-nocheck
import { localStorageKeys } from 'src/renderer2/common';
import { getLocal, setLocal } from 'src/renderer2/helper';
import { create } from 'zustand';

interface MenuState {
  openLeftPanel: boolean;
  closeWithoutAnimation: boolean;
  leftPanelData: any;
  displayLeftPanel: boolean;
  savedSearchProducts: any;
  selectedSavedSearch: any;
  setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => void;
  resetLeftPanelStore: () => void;
  setOpenLeftPanel: (openLeftPanel: boolean) => void;
  setLeftPanelData: (leftPanelData: any) => void; 
  setDisplayLeftPanel: (displayLeftPanel: boolean) => void;
  setSavedSearchProducts: (savedSearchProducts: any) => void;
  setSelectedSavedSearch: (selectedSavedSearch: any) => void;
}

const commonStore = {
    openLeftPanel:false,
    closeWithoutAnimation:false,
    leftPanelData: null,
    displayLeftPanel: false,
    savedSearchProducts: getLocal(localStorageKeys.instantPriceSearch, []),
    selectedSavedSearch: null,
}
  
  
  export const useLeftPanelStore = create<MenuState>((set, get) => ({
    ...commonStore,
    setOpenLeftPanel: (openLeftPanel: boolean) => set({ openLeftPanel }),
    setCloseWithoutAnimation: (closeWithoutAnimation: boolean) => set({ closeWithoutAnimation }),
    setLeftPanelData: (leftPanelData: any) => set({ leftPanelData }),
    setDisplayLeftPanel: (displayLeftPanel: boolean) => set({ displayLeftPanel }),
    setSavedSearchProducts: (savedSearchProducts: any) => {
      setLocal(localStorageKeys.instantPriceSearch, savedSearchProducts);
      set({ savedSearchProducts })
    },
    setSelectedSavedSearch: (selectedSavedSearch: any) => set({ selectedSavedSearch }),
    resetLeftPanelStore: () => set(state => ({
      ...commonStore
    })),
  }));
    