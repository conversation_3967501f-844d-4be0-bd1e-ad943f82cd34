import * as yup from "yup";

const isEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

export const companySchema = yup.object().shape({
  parentCompanyName: yup.string().required('Parent company name is required'),
  companyDBAName: yup.string().required('Company DBA name is required'),
  companyType: yup.string().required('Company type is required'),
  companyAddress: yup.object().shape({
    line1: yup.string().required('Line 1 is required'),
    line2: yup.string().optional(),
    city: yup.string().required('City is required'),
    state: yup.string().required('State is required'),
    stateCode: yup.string().required('State code is required'),
    zip: yup.string()
  }).required('Company address is required'),
  billingContactName: yup.string().required('Billing contact name is required'),
  billingContactEmail: yup.string().email('Invalid email format').required('Billing contact email is required').test('is-email', 'Invalid email format', function(value) {
    if(!value) return true;
    return isEmail(value);
  }),
  sendInvoicesTo: yup.string().trim().required('Send Invoices to is required').test('valid-emails', 'Send Invoices to is not valid', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
  sendRemittancesTo: yup.string().trim().required('Send Remittances to is required').test('valid-emails', 'Enter valid email', value => {
    if (!value) return true;
    const emails = value.split(',');
    const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
    return isValid;
  }),
  companyW9Form: yup.object().shape({
    file: yup.mixed(),
    cerificate_url_s3: yup.string().default(null).nullable(),
    file_name: yup.string().default(null).nullable(),
  }).required('W9 form is required'),

});

export type CompanyFormData = yup.InferType<typeof companySchema>;