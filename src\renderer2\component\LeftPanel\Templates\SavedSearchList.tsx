import { emojiRemoverRegex, priceUnits, useBuyerSettingStore, useGlobalStore } from "@bryzos/giss-ui-library";
import { fetchPrice } from "src/renderer2/helper";
import { useSearchStore } from "src/renderer2/store/SearchStore";
import { useLeftPanelStore } from "../LeftPanelStore";
import styles from "../ListTab/ListTab.module.scss";
import { ReactComponent as DeleteIcon } from "../../../assets/New-images/New-Image-latest/delete-outlined.svg";
import { ReactComponent as ShareIcon } from "../../../assets/New-images/New-Image-latest/share-outlined.svg";
import { ReactComponent as EditIcon } from "../../../assets/New-images/New-Image-latest/pencil-outlined.svg";
import { useEffect, useRef, useState } from "react";
import clsx from "clsx";

const SavedSearchList = ({ groupedData }: { groupedData: any }) => {
    const referenceData: any = useGlobalStore(state => state.referenceData);
    const productMapping = useGlobalStore(state => state.productMapping);
    const setShortListedSearchProductsData = useSearchStore.getState().setShortListedSearchProductsData;
    const shortListedSearchProductsData = useSearchStore.getState().shortListedSearchProductsData;
    const { buyerSetting } = useBuyerSettingStore.getState();
    const { selectedPriceUnit } = useSearchStore.getState();
    const { selectedSavedSearch, savedSearchProducts, setSavedSearchProducts } = useLeftPanelStore.getState();
    const setSelectedSavedSearch = useLeftPanelStore.getState().setSelectedSavedSearch;
    const searchZipCode = useSearchStore.getState().searchZipCode;
    const orderSizeSliderValue = useSearchStore.getState().orderSizeSliderValue;
    const [orderSizeList, setOrderSizeList] = useState<any[]>([]);
    
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState<string>('');
  const [selectedItem, setSelectedItem] = useState<any | null>(null);
  const editInputRef = useRef<HTMLInputElement>(null);

    useEffect(()=>{
        if(referenceData?.ref_weight_price_brackets?.length > 0){
            setOrderSizeList(referenceData.ref_weight_price_brackets)
        }
    },[referenceData])

    useEffect(() => {
        handleAddToSavedSearch();
    }, [shortListedSearchProductsData, selectedPriceUnit]);
    const handleAddToSavedSearch = async () => {
        const defaultZipCode = (buyerSetting as any)?.price_search_zip || '63105';
        const zipcode = searchZipCode?.length === 5 ? searchZipCode : defaultZipCode;
        
        const updatedProducts = shortListedSearchProductsData?.map(product => ({
            product_id: product.id,
            price_unit: selectedPriceUnit.toLowerCase(),
            price: String(product[`${selectedPriceUnit}_price`]) || "0"
        })) || [];
        if (selectedSavedSearch) {
            // Update products in selected saved search
            const updatedSearchProducts = savedSearchProducts.map((search: any) => {
                if (search.id === selectedSavedSearch.id) {

                    return {
                        ...search,
                        products: updatedProducts,
                        item_count: updatedProducts.length,
                        time_stamp: new Date().toISOString(),
                        zipcode: zipcode.trim(),
                        order_size: String(orderSizeSliderValue)
                        
                    };
                }
                return search;
            });

            setSavedSearchProducts(updatedSearchProducts);
        }else{
            const newSavedSearch = {
                title: 'Untitled',
                created_date: new Date().toISOString(), 
                search_date_time: new Date().toLocaleString(),
                zipcode: zipcode.trim(),
                order_size: String(orderSizeSliderValue),
                source: "search",
                products: updatedProducts,
                item_count: 1
            };
            setSavedSearchProducts([newSavedSearch, ...savedSearchProducts]);
            setSelectedSavedSearch(newSavedSearch);
        }
    }
    
    const handleItemClick = async (item: any) => {
        const selectedSavedSearch = useLeftPanelStore.getState().selectedSavedSearch;
        const setSelectedSavedSearch = useLeftPanelStore.getState().setSelectedSavedSearch;
        if(item.id === selectedSavedSearch?.id)return;
        setSelectedSavedSearch(item);
        const referenceData = useGlobalStore.getState().referenceData;
        const setOrderSizeSliderValue = useSearchStore.getState().setOrderSizeSliderValue;
        const setSelectedPriceUnit = useSearchStore.getState().setSelectedPriceUnit;
        const setSearchZipCode = useSearchStore.getState().setSearchZipCode;
        const brackets = referenceData?.ref_weight_price_brackets || [];
        let orderSize = Number(item.order_size);
        
        // Find the appropriate bracket based on order size
        const matchingBracket = getOrderSizeData(brackets, orderSize);

        if (matchingBracket) {
            orderSize = matchingBracket.min_weight;
        }
        setOrderSizeSliderValue(Number(orderSize));
        setSearchZipCode(item.zipcode);
        setSelectedPriceUnit(item.products[0].price_unit.toLowerCase() === priceUnits.ea ? priceUnits.pc : item.products[0].price_unit.toLowerCase());
        const setShortListedSearchProductsData = useSearchStore.getState().setShortListedSearchProductsData;
        const shortListedSearchProductsData = useSearchStore.getState().shortListedSearchProductsData;
        if (shortListedSearchProductsData.length > 0) {
            setShortListedSearchProductsData([]);
        }

        fetchPrice(item.products, item.zipcode, parseFloat(item.order_size.replace(/[$,]/g, "")));
       
    };

    const getOrderSizeData = (bracketList: any[], orderSize: number) => {
        return bracketList.find((bracket: any, index: number) => {
            // For last bracket, only check min since it's unlimited max
            if (index === bracketList.length - 1) {
                return orderSize >= Number(bracket.min_weight);
            }
            // For other brackets check if order size falls between min and max
            return orderSize >= Number(bracket.min_weight) && orderSize < Number(bracketList[index + 1].min_weight);
        });
    }

    
  const handleEditTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.target.value = e.target.value.replace(emojiRemoverRegex, '');
    setEditTitle(e.target.value);
  };
  
  // Handle keypress in edit mode
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };
    
  const handleSaveEdit = () => {
    if (editingItemId) {
      let updatedItem = {};
      // Update the history items with the new title
      const updatedItems = savedSearchProducts.map(item =>{
        if(item.id === editingItemId){
          item = { ...item, title: editTitle.trim() || 'Untitled' } 
          updatedItem = item;
        }
        return item;
      });
      setSavedSearchProducts(updatedItems);
      console.log('updatedItem ', updatedItem);
      const payload = {
        data: updatedItem
      };
      // saveSearchProductsMutation(payload);
      setEditingItemId(null);
      setEditTitle('');
    }
  };

  const handleCancelEdit = () => {
    setEditingItemId(null);
    setEditTitle('');
  };
  
  const handleEditClick = (e: React.MouseEvent, item: any) => {
    if(selectedSavedSearch?.id === item.id){
        e.stopPropagation(); // Prevent triggering the item click
        setEditingItemId(item.id);
        setEditTitle(item.title);
    }
  };

    return (
        <div className={styles.savedSearchListContainer}>
            {
                Object.keys(groupedData).length > 0 ?
                    Object.entries(groupedData).map(([label, items]: any, index: number) => (
                        <div key={index} className={styles.searchContainer}>
                            <p className={styles.searchLabel}>{label}</p>
                            {
                                items.map((item: any, index: number) => {
                                    const orderSizeData = getOrderSizeData(orderSizeList, Number(item.order_size));
                                    return (
                                    <div className={clsx(styles.searchItemContainer, selectedSavedSearch?.id === item.id && styles.selectedSearchItem)} key={index} onClick={() => handleItemClick(item)}>
                                        <div className={styles.searchTitle}>
                                            {editingItemId === item.id ? (
                                                <>
                                                    <div className={styles.editTitleRow}>
                                                        <input
                                                            ref={editInputRef}
                                                            type="text"
                                                            className={styles.editTitleInput}
                                                            value={editTitle}
                                                            onChange={handleEditTitleChange}
                                                            onKeyDown={handleKeyDown}
                                                            maxLength={30}
                                                        />
                                                        <div className={styles.editButtons}>
                                                            <button
                                                                className={styles.editButton}
                                                                onClick={handleCancelEdit}
                                                            >
                                                                Cancel
                                                            </button>
                                                            <button
                                                                className={`${styles.editButton} ${styles.saveButton}`}
                                                                onClick={handleSaveEdit}
                                                            >
                                                                Save
                                                            </button>
                                                        </div>
                                                    </div>
                                                </>
                                            ) : (
                                                <>
                                                    <span className={styles.searchTitleText}>
                                                        {item.title}
                                                        <span className={styles.editIcon} onClick={(e) => handleEditClick(e, item)}><EditIcon /></span>
                                                    </span>
                                                </>
                                            )}
                                            <span className={styles.itemCount}>{item.item_count} Items</span>
                                            <div className={styles.iconContainer}>
                                                <ShareIcon />
                                                <DeleteIcon />
                                            </div>
                                        </div>
                                        <div className={styles.searchDetails}>
                                            {item?.products?.length > 0 ?
                                                Array.from(new Set(
                                                    item?.products
                                                        .map((obj: any) => productMapping[obj.product_id]?.Key2 ?? '')
                                                )).join(', ')
                                            : '-'}
                                            <br/>
                                            {orderSizeData ? <span>Based Upon {Number(orderSizeData?.min_weight) === Number(orderSizeList[orderSizeList.length - 1].min_weight) ? Number(orderSizeData?.min_weight).toLocaleString() + '+' : `${Number(orderSizeData?.min_weight).toLocaleString()} to ${Number(orderSizeData?.max_weight).toLocaleString()}`} LBS</span> : '-'}<br />
                                            <span>{item.search_date_time}</span>
                                        </div>
                                        
                                    </div>
                                )})
                            }
                        </div>
                    ))
                    :
                    <div>
                        <p>Today</p>
                        <span>Your Instant Pricing activity will be saved here</span>
                    </div>
            }
        </div>
    );
}
export default SavedSearchList;