// import { <PERSON><PERSON><PERSON>, ChevronDownIcon, PlusIcon } from "lucide-react";
import React, { useRef, useState } from "react";
import styles from "./ShipmentListing.module.scss";
import { Dialog } from "@mui/material";
import ShipmentsTab from "../ShipmentsTab";
import { ReactComponent as CloseIcon } from '../../../../../assets/New-images/close-icon.svg';

const ShipmentListing = (): JSX.Element => {
  const shipmentListingRef = useRef(null);
  const [isCreateNewAddress, setIsCreateNewAddress] = useState(false);
  const [addressDialogOpen, setAddressDialogOpen] = useState(false);
  const [selectedShipment, setSelectedShipment] = useState(null);

  const shipmentList = [
    {
      nickname: "Rosslyn",
      address: {
        "line1": "st louirs",
        "line2": "line 2",
        "city": "city",
        "state_id": 27,
        "state_code": "NY",
        "zip": "12345"
    },
    user_delivery_receiving_availability_details:[
        {
            "id": "0ea51e07-3c72-48fe-ade9-c899aeeb5722",
            "user_id": "1775",
            "day": "Monday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Mon",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "2b5ba22b-ca6f-4351-819f-0476e5e7708f",
            "user_id": "1775",
            "day": "Tuesday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Tue",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "65619932-be71-456f-aa89-47c89889ac18",
            "user_id": "1775",
            "day": "Wednesday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Wed",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "d9cc250c-61ec-41ea-ae0c-f497b077fbde",
            "user_id": "1775",
            "day": "Thursday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Thu",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "98333730-2707-463c-af80-8ae682aba55c",
            "user_id": "1775",
            "day": "Friday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Fri",
            "is_user_available": 1,
            "super_admin_user_id": null
        }
    ],
      appointmentRequired: false,
      deliveryContact: {
        firstName: "Bob",
        lastName: "Evans",
        email: "<EMAIL>",
        phone: "(*************",
      },
      shippingDocsEmail: "<EMAIL>",
      isDefault: true,
    },
    {
      nickname: "Rosslyn 2",
      address: {
        "line1": "st louirs",
        "line2": "line 2",
        "city": "city",
        "state_id": 27,
        "state_code": "NY",
        "zip": "12345"
    },
    user_delivery_receiving_availability_details:[
        {
            "id": "0ea51e07-3c72-48fe-ade9-c899aeeb5722",
            "user_id": "1775",
            "day": "Monday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Mon",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "2b5ba22b-ca6f-4351-819f-0476e5e7708f",
            "user_id": "1775",
            "day": "Tuesday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Tue",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "65619932-be71-456f-aa89-47c89889ac18",
            "user_id": "1775",
            "day": "Wednesday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Wed",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "d9cc250c-61ec-41ea-ae0c-f497b077fbde",
            "user_id": "1775",
            "day": "Thursday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Thu",
            "is_user_available": 1,
            "super_admin_user_id": null
        },
        {
            "id": "98333730-2707-463c-af80-8ae682aba55c",
            "user_id": "1775",
            "day": "Friday",
            "from": "7",
            "to": "16",
            "is_active": 1,
            "created_date": "2025-02-25 14:44:02",
            "time_stamp": "2025-05-23 10:40:38",
            "display_name": "Fri",
            "is_user_available": 1,
            "super_admin_user_id": null
        }
    ],
      appointmentRequired: false,
      deliveryContact: {
        firstName: "Bob",
        lastName: "Evans",
        email: "<EMAIL>",
        phone: "(*************",
      },
      shippingDocsEmail: "<EMAIL>",
      isDefault: true,
    }
  ];

  const handleAddressDialog = (isCreateNewAddress: boolean, shipmentDetails: any = null) => {
    setAddressDialogOpen(true);
    setIsCreateNewAddress(isCreateNewAddress);
    setSelectedShipment(shipmentDetails);
  }

  return (
    <div className={styles.container} ref={shipmentListingRef}>
      <div className={styles.navigationSection}>

        {/* Create New Ship-to button */}
        <button className={styles.createButton} onClick={() => handleAddressDialog(true)}>
          Create New Ship-to
          <div className={styles.createButtonIcon}>
            {/* <PlusIcon className={styles.plusIcon} /> */}
          </div>
        </button>
      </div>

      {/* Shipments Table */}
      <div className={styles.tableCard}>
        <table className={styles.table}>
          <thead className={styles.tableHeader}>
            <tr className={styles.tableHeaderRow}>
              <th className={styles.tableHeaderCell}>
                LOCATION
                <br />
                NICKNAME
              </th>
              <th className={styles.tableHeaderCell}>
                ADDRESS
              </th>
              <th className={styles.tableHeaderCell}>
                HOURS
              </th>
              <th className={styles.tableHeaderCell}>
                APPT
                <br />
                REQ&apos;D
              </th>
              <th className={styles.tableHeaderCell}>
                DELIVERY
                <br />
                CONTACT
              </th>
              <th className={styles.tableHeaderCell}>
                EMAIL
                <br />
                DOCS TO
              </th>
              <th className={styles.tableHeaderCell}>
                SET AS
                <br />
                DEFAULT
              </th>
              <th className={styles.tableHeaderCell} style={{ width: '80px' }}></th>
            </tr>
          </thead>
          <tbody className={styles.tableBody}>
          {shipmentList.map((shipmentLocation, index) => (
              <React.Fragment key={shipmentLocation.nickname || index}>
                <tr className={styles.tableRow}>
                  <td className={styles.tableCell}>
                    {shipmentLocation.nickname}
                  </td>
                  <td className={`${styles.tableCell} ${styles.addressCell}`}>
                    {shipmentLocation.address.line1}
                    {shipmentLocation.address.line2 && <br />}
                    {shipmentLocation.address.line2}
                    {shipmentLocation.address.city && <br />}
                    {shipmentLocation.address.city}
                    {shipmentLocation.address.state_code && <br />}
                    {shipmentLocation.address.state_code}
                  </td>
                  <td className={`${styles.tableCell} ${styles.hoursCell}`}>
                    {shipmentLocation.user_delivery_receiving_availability_details.map((hour, hourIndex) => (
                      <div key={hourIndex}>{hour.display_name} {hour.from} - {hour.to}</div>
                    ))}
                  </td>
                  <td className={styles.tableCell}>
                    {shipmentLocation.appointmentRequired ? "Yes" : "No"}
                  </td>
                  <td className={`${styles.tableCell} ${styles.contactCell}`}>
                    <div>{shipmentLocation.deliveryContact.firstName} {shipmentLocation.deliveryContact.lastName}</div>
                    <a
                      href={`mailto:${shipmentLocation.deliveryContact.email}`}
                    >
                      {shipmentLocation.deliveryContact.email}
                    </a>
                    <div>{shipmentLocation.deliveryContact.phone}</div>
                  </td>
                  <td className={`${styles.tableCell} ${styles.emailCell}`}>
                    <a
                      href={`mailto:${shipmentLocation.shippingDocsEmail}`}
                    >
                      {shipmentLocation.shippingDocsEmail}
                    </a>
                  </td>
                  <td className={`${styles.tableCell} ${styles.center}`}>
                    {shipmentLocation.isDefault && (
                      <span className={styles.defaultIndicator}>Default</span>
                    )}
                  </td>
                  <td className={styles.tableCell}>
                    <button className={styles.editButton} onClick={() => handleAddressDialog(false , shipmentLocation)}>
                      Edit
                    </button>
                  </td>
                </tr>
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
      <Dialog
        open={addressDialogOpen}
        onClose={(event) =>  setAddressDialogOpen(false)}
        transitionDuration={100}
        disableScrollLock={true}
        container={shipmentListingRef.current}
        
                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid transparent',
                    borderRadius: '0px 0px 20px 20px',
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0
                    }
                }}
                hideBackdrop
                classes={{
                    root: styles.customeAddressPopup,
                    paper: styles.dialogContent
                }}
      >
        <button className={styles.closeIcon} onClick={(event) => setAddressDialogOpen(false)}><CloseIcon /></button>
        <ShipmentsTab selectedShipment={selectedShipment} isCreate={isCreateNewAddress} closeDialog={() => setAddressDialogOpen(false)}/> 
      </Dialog>
      
    </div>
  );
};

export default ShipmentListing;