  .searchSection {
    padding: 0px 16px;
    display: flex;

    .zipBox {
      display: flex;
      flex-direction: column;
      width: 90px;
      height: 41px;
      margin: 0px 30px 0px 0px;
      padding:6px 16px 4.9px 16px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.04);
      transition: background-color 0.1s ease-in-out;
      flex: 0 0 auto;
   
      .destZIP {
        font-family: Syncopate;
        font-size: 11px;
        font-weight: normal;
        letter-spacing: -0.44px;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
      }

      .zipCode {
        display: flex;
        justify-content: center;
        width: 100%;
        line-height: 1;
        input {
          background-color: transparent;
          border: 0px;
          width: 100%;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: normal;
          text-align: center;
          padding: 0px 0px 0px 0px;
          background-image: linear-gradient(to right, #99762f -26%, #ffc44f 30%, #ffc44f 71%, #99762f 127%);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          color: #ffc44f;
          letter-spacing: 2.4px;
          position: relative;
          left: 1px;
          &:focus {
            outline: none;
          }
        }

      }

      .zipInput {
        text-align: center;
        padding: 0;
        margin: 0;
        border: none;
        background: transparent;
      }

    }

    .searchBox {
      width: 100%;
      height: 41px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 16px;
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.04);
      transition: background-color 0.1s ease-in-out;
      z-index: 2;
    
      &:focus-within {
        background: url('../../../assets/New-images/SearchInputActive.svg') no-repeat bottom;
        background-size: cover;
      }
    
      input {
        background-color: transparent;
        border: none;
        width: 100%;
        height: 100%;
        padding: 6px 12px 6px 0;
        font-family: Inter;
        font-size: 15px;
        font-weight: normal;
        letter-spacing: 0.6px;
        color: #1fbbfe;
        transition: all 0.1s ease-in-out;
    
        &::placeholder {
          color: #616575;
        }
    
        &:focus {
          outline: none;
        }
      }
    
      svg {
        margin-left: 18px;
      }
    }
    
  }

  .searchFilterSection {
    display: flex;
    padding: 16px;
    gap: 12px;
    .zipBox {
      display: flex;
      flex-direction: column;
      .destZIP {
        background-color: #2b2c32;
        font-family: Inter;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.48px;
        text-align: left;
        color: #9b9eac;
        padding: 7px 0px 8px 12px;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
      }
      .zipCode {
        input {
          background: #303136;
          height: 34px;
          border: none;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: normal;
          letter-spacing: 0.56px;
          text-align: left;
          color: #fff;
          padding-left: 12px;
          border-bottom-right-radius: 10px;
          border-bottom-left-radius: 10px;
        }
      }
    }
    .dropdownBox {
      display: flex;
      flex-direction: column;
      min-width: 150px;
      .dropdownLabel {
        font-family: Inter;
        font-size: 12px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.48px;
        text-align: left;
        color: #9b9eac;
        padding: 7px 0px 8px 12px;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
        background-color: #2b2c32;
      }
      .dropdownValue {
          background: #303136;
          height: 34px;
          border: none;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          font-stretch: normal;
          font-style: normal;
          line-height: normal;
          letter-spacing: 0.56px;
          text-align: left;
          color: #fff;
          padding-left: 12px;
          border-radius: 0px 0px 10px 10px;
      }
    }
  }
  .dropDownBG.dropDownBG {
    width: 110px;
    z-index: 999;
    padding: 4px;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: #9c9da5;
    
        ul {
            padding: 0px;
    
            li {
            font-family: Inter;
            font-size: 14px;
            font-weight: 500;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: center;
            color: #191a20;
            margin-bottom: 2px;
            &[aria-selected="true"] {
                border-radius: 6px;
                background-color: #e0e0e0;
                font-weight: bold;
            }
            &:hover {
                border-radius: 6px;
                background-color: #e0e0e0;
                font-weight: bold;
            }
            }
        }
    }