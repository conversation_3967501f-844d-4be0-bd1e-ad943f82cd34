.container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 607.8px;
}

.inputContainer {
  width: 100%;
}

.input {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  border: 1px solid #3e3e44;
  border-radius: 6px;
  background-color: #2a2a2e;
  color: #ffffff;
  font-family: Syncopate;
  font-size: 14px;
  font-weight: normal;
  transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;

  &::placeholder {
    color: #888888;
  }

  &:focus {
    outline: none;
    border-color: #459fff;
    background-color: #3e3e44;
  }
}

.inputFocused {
  border-color: #459fff;
  background-color: #3e3e44;
}

.inputError {
  border-color: #ff4444;
}

.inputWithSelectedStates {
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.selectedStatesContainer {
  background-color: #2a2a2e;
  border: 1px solid #3e3e44;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  padding: 8px 12px;
  min-height: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.selectedStateTag {
  background-color: #459fff;
  color: #ffffff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-family: Syncopate;
  display: inline-block;
}

.statesGrid {
  display: grid;
  grid-template-columns: repeat(17, 1fr);
  gap: 2px;
  padding: 4px;
  background-color: #2a2a2e;
  border-radius: 8px;
  width: 100%;
}

.stateItem {
  width: 33.2px;
  height: 24.1px;
  padding: 2px 2px;
  border-radius: 2.4px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  font-family: Syncopate;
  font-size: 12.1px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.48px;
  text-align: left;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
  border: 1px solid transparent;
  background: transparent;

  &:focus {
    background-color: #3e3e44;
  }

  &:hover {
    background-color: #3e3e44;
  }
}

// Keyboard navigation hover effect
.hovered {
  background-color: #459fff !important;
  color: #ffffff !important;
  border: 1px solid #459fff;
}

// Default state - grey
.default {
  color: #888888;
}

// Selected state - blue background
.selected {
  background-color: #459fff !important;
  color: #ffffff !important;

  &:hover {
    background-color: #459fff;
  }
}

// Exact match - blue text
.exactMatch {
  color: #459fff;
  font-weight: 600;
}

// Starts with match - white text
.startsWithMatch {
  color: #ffffff;
}

// No match - grey text but still visible
.noMatch {
  color: #888888;
  opacity: 0.6;
} 